package game

import (
	"context"
	"fmt"
	"math/rand"
	"sort"
	"train/base/structs"
	"train/common/pb"
	"train/db"
	"train/db/lua"
	ut "train/utils"

	"github.com/huyangv/vmqant/log"
	"github.com/redis/go-redis/v9"
	"github.com/samber/lo"
	"github.com/spf13/cast"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"google.golang.org/protobuf/reflect/protoreflect"
)

// InitPvpHD 自动生成，不要在这个方法添加任何内容。
func InitPvpHD(this *Game) {
	// 更新竞技场阵容
	this.middleware.Wrap("C2S_UpdateFormationMessage", this.C2sUpdateFormationMessageHandler)
	// 获取竞技场排名
	this.middleware.Wrap("C2S_GetRankListMessage", this.C2sGetRankListMessageHandler)
	// 获取竞技场对手
	this.middleware.Wrap("C2S_GetRivalMessage", this.C2sGetRivalMessageHandler)
	// 竞技场挑战
	this.middleware.Wrap("C2S_PvpFightMessage", this.C2sPvpFightMessageHandler)
	// 获取战绩列表
	this.middleware.Wrap("C2S_PvpBattleRecordListMessage", this.C2sPvpBattleRecordListMessageHandler)
	// pvp战斗重放
	this.middleware.Wrap("C2S_PvpBattleReplayMessage", this.C2sPvpBattleReplayMessageHandler)
	// pvp模块数据
	this.middleware.Wrap("C2S_PvpModuleDataMessage", this.C2sPvpModuleDataMessageHandler)
}
func (this *Game) C2sUpdateFormationMessageHandler(player *structs.Player, msg *pb.C2S_UpdateFormationMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间
	typ := msg.GetType()
	ids := ut.ToInt(msg.GetIdAry())

	if len(ids) > 5 {
		return &pb.S2C_UpdateFormationMessage{Code: 2}
	}
	for _, id := range ids {
		passenger := player.GetPassengerById(int(id))
		if passenger == nil {
			return &pb.S2C_UpdateFormationMessage{Code: 3}
		}
	}

	succ := false
	if typ == pb.PvpType_NORMAL {
		succ = player.UpdateNormalPvpRoles(ids)
	}

	return &pb.S2C_UpdateFormationMessage{Code: lo.If[int32](succ, 0).Else(1)}
	//@action-code-end
}
func (this *Game) C2sGetRankListMessageHandler(player *structs.Player, msg *pb.C2S_GetRankListMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间
	sid := player.ServerId
	typ := msg.GetType()
	var r *redis.ZSliceCmd

	myRank := 0
	myScore := 0

	if typ == pb.PvpType_NORMAL {
		r = db.GetRedis().ZRevRangeWithScores(context.TODO(), db.RKPvpNormalRank(sid), 0, 19)
		data, vRank := player.GetNormalPvpData()
		myRank = vRank
		myScore = int(data.Score)
	}

	if r.Err() != nil {
		log.Error(r.Err().Error())
		return &pb.S2C_GetRankListMessage{Code: 1}
	}
	ranks := r.Val()

	return &pb.S2C_GetRankListMessage{
		Rank:  int32(myRank),
		Score: int32(myScore),
		List: lo.Map(ranks, func(v redis.Z, i int) *pb.PvpSimplePlayerData {
			id := v.Member.(string)

			tmpPlr := &structs.Player{Id: id}

			return &pb.PvpSimplePlayerData{
				Ext:   tmpPlr.ToSimpleData(),
				Score: cast.ToInt32(v.Score),
				Rank:  int32(i) + 1,
			}
		}),
	}
	//@action-code-end
}
func (this *Game) C2sGetRivalMessageHandler(player *structs.Player, msg *pb.C2S_GetRivalMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间
	sid := player.ServerId
	typ := msg.GetType()

	if typ == pb.PvpType_NORMAL {
		data, _ := player.GetNormalPvpData()
		if data == nil {
			return &pb.S2C_GetRivalMessage{Code: 1}
		}
		if !msg.GetRefresh() && len(data.Rivals) >= 3 {
			return &pb.S2C_GetRivalMessage{
				List: fillPlayerDataByAry(data.Rivals, sid),
			}
		}

		key := db.RKPvpNormalRank(sid)
		score := float64(data.Score)

		exclude := make([]string, 0)
		exclude = append(exclude, player.Id)

		// 积分相近
		id := randomF(key, score, 0.95, 1.05, -0.05, 0.05, exclude, 50)
		if id == "" {
			panic("error usr id")
		}
		exclude = append(exclude, id)
		// 积分高
		id = randomF(key, score, 1.2, 1.3, 0, 0.1, exclude, 3)
		if id == "" {
			id = randomF(key, score, 0.95, 1.05, -0.05, 0.05, exclude, 30)
		}
		// 积分低
		exclude = append(exclude, id)
		id = randomF(key, score, 0.95, 1.05, -0.1, 0, exclude, 3)
		if id == "" {
			id = randomF(key, score, 0.95, 1.05, -0.05, 0.05, exclude, 30)
		}
		exclude = append(exclude, id)
		exclude = exclude[1:]
		res := fillPlayerDataByAry(exclude, sid)

		data.Rivals = exclude
		serializer := ut.NewRedisHashSerializer(db.GetRedis())
		e := serializer.HSetField(context.TODO(), db.RKPvpNormalDataOf(player.Id), "rivals", exclude)
		if e != nil {
			log.Error("更新普通竞技场对手失败:%s", e.Error())
		}
		return &pb.S2C_GetRivalMessage{
			List: res,
		}
	}

	return &pb.S2C_GetRivalMessage{
		Code: 2,
	}
	//@action-code-end
}
func (this *Game) C2sPvpFightMessageHandler(player *structs.Player, msg *pb.C2S_PvpFightMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间
	typ := msg.GetType()
	result := int(msg.GetResult())
	rivalIndex := int(msg.GetRivalIndex())
	rank := 0
	score := 0

	if typ == pb.PvpType_NORMAL {
		data, _ := player.GetNormalPvpData()
		if data == nil {
			return &pb.S2C_PvpFightMessage{Code: 1}
		}
		if player.Pvp.GetTicket(typ) <= 0 {
			return &pb.S2C_PvpFightMessage{Code: 2}
		}

		if rivalIndex >= len(data.Rivals) || rivalIndex < 0 {
			return &pb.S2C_PvpFightMessage{Code: 3}
		}
		rival := data.Rivals[rivalIndex]
		if rival == "" {
			panic("错误的对手id ??? ")
		}

		rivalData := fillPlayerData(rival, player.ServerId)
		if rivalData == nil {
			// 没有对手数据？
			return &pb.S2C_PvpFightMessage{Code: 4}
		}
		ids := player.Battle.GetTeam(0)
		roles := lo.Map(ids.Uids, func(id string, i int) *pb.BattleRole {
			ps := player.GetPassengerById(cast.ToInt(id))
			return ps.ToBattleRole().ToPb()
		})

		// 排行榜key
		sid := player.ServerId
		rankKey := db.RKPvpNormalRank(sid)

		// 数据key
		myKey := db.RKPvpNormalDataOf(player.Id)
		rivalKey := db.RKPvpNormalDataOf(rival)
		// 原来的积分
		myScore := 0
		rivalScore := 0

		timeScore := ut.TimeScore()

		// 使用Lua脚本原子性更新积分，传递战斗结果让Lua自己计算changeValue
		luaResult, err := db.GetRedis().Eval(context.TODO(),
			lua.UpdatePvpScoreScript,
			[]string{myKey, rivalKey, rankKey},
			player.Id, rival, result, timeScore,
		).Result()

		if err != nil {
			log.Error("更新积分失败:%s", err.Error())
			return &pb.S2C_PvpFightMessage{Code: 5}
		}
		resultArray, ok := luaResult.([]interface{})
		if !ok {
			log.Error("更新积分失败:返回结果不是数组")
			return &pb.S2C_PvpFightMessage{Code: 5}
		}
		code := cast.ToInt(resultArray[0])
		if code != 0 {
			errorMsg := cast.ToString(resultArray[1])
			log.Error("更新积分失败:%d --> %s", code, errorMsg)
		}

		newMyScore := cast.ToFloat64(resultArray[1])
		_ = cast.ToFloat64(resultArray[2])      // newRivalScore，暂时不需要使用
		myScore = cast.ToInt(resultArray[3])    // 原始我方积分
		rivalScore = cast.ToInt(resultArray[4]) // 原始对手积分
		changeValue := cast.ToInt(resultArray[5])
		newRank := cast.ToInt(resultArray[6])

		log.Debug("更新pvp积分,挑战者:%s, 对手:%s, 原积分:%d, 分数变动:%d, 新积分:%.0f, 新排名: %d", player.Id, rival, myScore, changeValue, newMyScore, newRank)

		// 扣除门票
		player.Pvp.AddTicket(typ, -1)
		// 记录
		now := ut.Now()
		// 平局 还是记录成失败 但是不扣分
		battleResult := result
		if result == 3 {
			battleResult = 2
		}
		pData := &db.PvpBattleRecord{
			Attacker:    player.Id,
			Defender:    rival,
			Result:      battleResult,
			Timestamp:   now,
			ScoreChange: changeValue,
			Score1:      myScore,
			Score2:      rivalScore,
			AttackRoles: roles,
			DefendRoles: rivalData.BattleRoles,
		}

		db.PVP_RECORD_NORMAL.GetCollection().InsertOne(context.TODO(), &pData, &options.InsertOneOptions{})
		// 清空对手
		data.Rivals = data.Rivals[0:0]
		score = int(newMyScore)
		rank = int(newRank)
	}

	return &pb.S2C_PvpFightMessage{
		Rank:  int32(rank),
		Score: int32(score),
	}
	//@action-code-end
}
func (this *Game) C2sPvpBattleRecordListMessageHandler(player *structs.Player, msg *pb.C2S_PvpBattleRecordListMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间
	id := player.Id
	// 作为攻击者的记录
	attackRecords, _ := db.PVP_RECORD_NORMAL.GetCollection().Find(
		context.TODO(),
		bson.M{"attacker": id},
		&options.FindOptions{
			Sort:  bson.M{"timestamp": -1},
			Limit: lo.ToPtr(int64(20)),
		},
	)

	// 作为防守者的记录
	defendRecords, _ := db.PVP_RECORD_NORMAL.GetCollection().Find(
		context.TODO(),
		bson.M{"defender": id},
		&options.FindOptions{
			Sort:  bson.M{"timestamp": -1},
			Limit: lo.ToPtr(int64(20)),
		},
	)

	defer attackRecords.Close(context.TODO())
	defer defendRecords.Close(context.TODO())
	res := make([]*pb.PvpBattleRecordData, 0, 40)

	deal := func(cur *mongo.Cursor) {
		for cur.Next(context.TODO()) {
			var r bson.M
			cur.Decode(&r)
			attackerId := r["attacker"]
			defenderId := r["defender"]
			result := r["result"]
			timestamp := r["timestamp"]
			scoreChange := r["scoreChange"]
			score1 := r["score1"]
			score2 := r["score2"]
			res = append(res, &pb.PvpBattleRecordData{
				DocId:       r["_id"].(primitive.ObjectID).Hex(),
				Attacker:    fillPlayerData(attackerId.(string), player.ServerId),
				Defender:    fillPlayerData(defenderId.(string), player.ServerId),
				Result:      result.(int32),
				Time:        int32((int64(ut.Now()) - timestamp.(int64))),
				ScoreChange: scoreChange.(int32),
				Score1:      score1.(int32),
				Score2:      score2.(int32),
			})
		}
	}

	deal(attackRecords)
	deal(defendRecords)

	sort.Slice(res, func(i, j int) bool {
		return res[i].Time < res[j].Time
	})
	if len(res) > 20 {
		res = res[:20]
	}
	return &pb.S2C_PvpBattleRecordListMessage{
		List: res,
	}
	//@action-code-end
}
func (this *Game) C2sPvpBattleReplayMessageHandler(player *structs.Player, msg *pb.C2S_PvpBattleReplayMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间
	docId := msg.GetDocId()
	hex, err := primitive.ObjectIDFromHex(docId)
	if err != nil {
		return &pb.S2C_PvpBattleReplayMessage{Code: 1}
	}
	cur := db.PVP_RECORD_NORMAL.GetCollection().FindOne(context.TODO(), bson.M{"_id": hex})
	var result *db.PvpBattleRecord
	err = cur.Decode(&result)
	if err != nil {
		return &pb.S2C_PvpBattleReplayMessage{Code: 2}
	}
	myId := player.Id
	attacker := result.Attacker
	defender := result.Defender
	if myId != attacker && myId != defender {
		return &pb.S2C_PvpBattleReplayMessage{Code: 3}
	}

	return &pb.S2C_PvpBattleReplayMessage{
		Attacker: result.AttackRoles,
		Defender: result.DefendRoles,
	}
	//@action-code-end
}
func (this *Game) C2sPvpModuleDataMessageHandler(player *structs.Player, msg *pb.C2S_PvpModuleDataMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间
	normal, rank := player.GetNormalPvpData()
	pbNormal := normal.ToPb()
	pbNormal.Rank = int32(rank)

	return &pb.S2C_PvpModuleDataMessage{
		Normal: pbNormal,
	}
	//@action-code-end
}

//@logic-code-start 自定义代码必须放在start和end之间

func fillPlayerDataByAry(ary []string, sid int) []*pb.PvpSimplePlayerData {
	res := make([]*pb.PvpSimplePlayerData, 0)
	for _, id := range ary {
		res = append(res, fillPlayerData(id, sid))
	}
	return res
}

func fillPlayerData(id string, sid int) *pb.PvpSimplePlayerData {
	tmpPlr := &structs.Player{Id: id, ServerId: sid}
	return tmpPlr.ToPvpSimpleData()
}

func randomF(key string, score, minRate, maxRate float64, minStep, maxStep float64, exclude []string, recursion int) string {
	if recursion <= 0 {
		return ""
	}
	recursion--
	min := score * minRate
	max := score * maxRate
	count := db.GetRedis().ZCount(context.TODO(), key,
		fmt.Sprintf("%f", min),
		fmt.Sprintf("%f", max)).Val()
	if count > 0 {
		// 随机一个offset
		offset := rand.Int63n(count)
		// 用offset获取一个随机成员
		result := db.GetRedis().ZRevRangeByScoreWithScores(context.TODO(), key, &redis.ZRangeBy{
			Min:    fmt.Sprintf("%f", min),
			Max:    fmt.Sprintf("%f", max),
			Offset: offset,
			Count:  1,
		})
		ary := result.Val()
		if len(ary) > 0 {
			item := ary[0]
			_, is := lo.Find(exclude, func(i string) bool {
				return i == item.Member.(string)
			})
			if !is {
				return item.Member.(string)
			}
		}
	}
	return randomF(key, score, minRate+minStep, maxRate+maxStep, minStep, maxStep, exclude, recursion)
}

//@logic-code-end

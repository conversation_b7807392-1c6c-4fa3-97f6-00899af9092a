-- PVP积分更新脚本
-- KEYS[1]: 本人数据key (myKey)
-- KEYS[2]: 对手数据key (rivalKey)
-- KEYS[3]: 分区排行榜key (rankKey)
-- ARGV[1]: 本人ID (myId)
-- ARGV[2]: 对手ID (rivalId)
-- ARGV[3]: 战斗结果 (result: 1=胜利, 2=失败, 3=平局)
-- ARGV[4]: 时间分数 (timeScore)

local myKey = KEYS[1]
local rivalKey = KEYS[2]
local rankKey = KEYS[3]
local myId = ARGV[1]
local rivalId = ARGV[2]
local result = tonumber(ARGV[3])
local timeScore = tonumber(ARGV[4])

-- Lua中的max函数
local function max(a, b)
    if a > b then
        return a
    else
        return b
    end
end

-- 获取当前积分
local myScore = redis.call('HGET', myKey, 'score')
local rivalScore = redis.call('HGET', rivalKey, 'score')

-- 如果积分不存在，返回错误
if myScore == false then
    return {-1, "挑战者积分数据不存在"}
end

if rivalScore == false then
    return {-2, "对手积分数据不存在"}
end

-- 转换为数字
myScore = tonumber(myScore)
rivalScore = tonumber(rivalScore)

-- 计算积分变化值
local changeValue = 0
if result == 1 then
    -- 胜利: changeValue = max(2, (rivalScore - myScore) * 0.25)
    changeValue = max(2, math.floor((rivalScore - myScore) * 0.25))
elseif result == 2 then
    -- 失败: changeValue = -max(1, (myScore - rivalScore) * 0.1)
    changeValue = -max(1, math.floor((myScore - rivalScore) * 0.1))
else
    -- 平局或其他情况，不变动积分
    changeValue = 0
end

-- 计算新积分
local newMyScore = myScore + changeValue + timeScore
local newRivalScore = rivalScore - changeValue + timeScore

-- 更新挑战者积分
redis.call('HSET', myKey, 'score', newMyScore)
-- 更新对手积分
redis.call('HSET', rivalKey, 'score', newRivalScore)
-- 更新排行榜
redis.call('ZADD', rankKey, newMyScore, myId)
redis.call('ZADD', rankKey, newRivalScore, rivalId)
-- 获取新的排名
local myRank = redis.call('ZREVRANK', rankKey, myId)

-- 返回成功结果和新积分，包含计算出的changeValue
return {0, newMyScore, newRivalScore, myScore, rivalScore, changeValue, myRank}

-- PVP积分更新脚本
-- KEYS[1]: 挑战者本人数据key (myKey)
-- KEYS[2]: 对手数据key (rivalKey)  
-- KEYS[3]: 排行榜key (rankKey)
-- ARGV[1]: 挑战者ID (playerId)
-- ARGV[2]: 对手ID (rivalId)
-- ARGV[3]: 积分变化值 (changeValue)
-- ARGV[4]: 时间分数 (timeScore)

local myKey = KEYS[1]
local rivalKey = KEYS[2]
local rankKey = KEYS[3]
local playerId = ARGV[1]
local rivalId = ARGV[2]
local changeValue = tonumber(ARGV[3])
local timeScore = tonumber(ARGV[4])

-- 获取当前积分
local myScore = redis.call('HGET', myKey, 'score')
local rivalScore = redis.call('HGET', rivalKey, 'score')

-- 如果积分不存在，返回错误
if myScore == false then
    return {-1, "挑战者积分数据不存在"}
end

if rivalScore == false then
    return {-2, "对手积分数据不存在"}
end

-- 转换为数字
myScore = tonumber(myScore)
rivalScore = tonumber(rivalScore)

-- 计算新积分
local newMyScore = myScore + changeValue + timeScore
local newRivalScore = rivalScore - changeValue + timeScore

-- 原子性更新所有数据
-- 更新挑战者积分
redis.call('HSET', myKey, 'score', newMyScore)
-- 更新对手积分
redis.call('HSET', rivalKey, 'score', newRivalScore)
-- 更新排行榜
redis.call('ZADD', rankKey, newMyScore, playerId)
redis.call('ZADD', rankKey, newRivalScore, rivalId)

-- 返回成功结果和新积分
return {0, newMyScore, newRivalScore, myScore, rivalScore}

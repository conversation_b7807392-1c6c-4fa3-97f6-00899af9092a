-- PVP积分更新脚本
-- KEYS[1]: 本人数据key (myKey)
-- KEYS[2]: 对手数据key (rivalKey)
-- KEYS[3]: 分区排行榜key (rankKey)
-- ARGV[1]: 本人ID (myId)
-- ARGV[2]: 对手ID (rivalId)
-- ARGV[3]: 战斗结果 (result: 1=胜利, 2=失败, 3=平局)
-- ARGV[4]: 时间分数 (timeScore)

local myKey = KEYS[1]
local rivalKey = KEYS[2]
local rankKey = KEYS[3]
local myId = ARGV[1]
local rivalId = ARGV[2]
local result = tonumber(ARGV[3])
local timeScore = tonumber(ARGV[4])

-- Lua中的max函数
local function max(a, b)
    if a > b then
        return a
    else
        return b
    end
end

-- 获取当前积分
local myScore = redis.call('HGET', myKey, 'score')
local rivalScore = redis.call('HGET', rivalKey, 'score')

-- 如果积分不存在，返回错误
if myScore == false then
    return {-1, "挑战者积分数据不存在"}
end

if rivalScore == false then
    return {-2, "对手积分数据不存在"}
end

-- 转换为数字（保持浮点数精度）
myScore = tonumber(myScore)
rivalScore = tonumber(rivalScore)

-- 计算积分变化值时使用整数部分
local myScoreInt = math.floor(myScore)
local rivalScoreInt = math.floor(rivalScore)

local changeValue = 0
if result == 1 then
    -- 胜利: changeValue = max(2, floor((rivalScoreInt - myScoreInt) * 0.25))
    local temp = (rivalScoreInt - myScoreInt) * 0.25
    changeValue = max(2, math.floor(temp)) -- changeValue必须是整数，向下取整
elseif result == 2 then
    -- 失败: changeValue = -max(1, floor((myScoreInt - rivalScoreInt) * 0.1))
    local temp = (myScoreInt - rivalScoreInt) * 0.1
    changeValue = -max(1, math.floor(temp)) -- changeValue必须是整数，向下取整
else
    -- 平局或其他情况，不变动积分
    changeValue = 0
end

-- 计算新积分
local newMyScore = myScore + changeValue + timeScore
local newRivalScore = rivalScore - changeValue + timeScore

-- 调试信息：记录计算过程
redis.call('HSET', 'debug_pvp_' .. myId, 'last_calc',
    string.format("result=%d,myScore=%.6f,myScoreInt=%d,rivalScore=%.6f,rivalScoreInt=%d,changeValue=%d,timeScore=%.6f,newMyScore=%.6f",
    result, myScore, myScoreInt, rivalScore, rivalScoreInt, changeValue, timeScore, newMyScore))

-- 更新挑战者积分
redis.call('HSET', myKey, 'score', newMyScore)
-- 更新对手积分
redis.call('HSET', rivalKey, 'score', newRivalScore)
-- 更新排行榜
redis.call('ZADD', rankKey, newMyScore, myId)
redis.call('ZADD', rankKey, newRivalScore, rivalId)
-- 获取新的排名
local myRank = redis.call('ZREVRANK', rankKey, myId)

-- 返回成功结果和新积分，包含计算出的changeValue
-- 返回格式: {状态码, 新我方积分, 新对手积分, 原我方积分, 原对手积分, 积分变化, 新排名}
return {0, newMyScore, newRivalScore, myScore, rivalScore, changeValue, myRank + 1}

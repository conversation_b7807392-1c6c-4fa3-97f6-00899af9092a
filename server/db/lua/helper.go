package lua

import (
	"fmt"
	"github.com/spf13/cast"
)

// PvpScoreUpdateResult PVP积分更新结果
type PvpScoreUpdateResult struct {
	Success       bool
	ErrorMessage  string
	NewMyScore    float64
	NewRivalScore float64
	OldMyScore    int
	OldRivalScore int
}

// ParsePvpScoreUpdateResult 解析PVP积分更新Lua脚本的返回结果
func ParsePvpScoreUpdateResult(result interface{}) (*PvpScoreUpdateResult, error) {
	resultArray, ok := result.([]interface{})
	if !ok {
		return nil, fmt.Errorf("invalid result format")
	}
	
	if len(resultArray) < 2 {
		return nil, fmt.Errorf("result array too short")
	}
	
	code := cast.ToInt(resultArray[0])
	if code != 0 {
		// 失败情况
		errorMsg := ""
		if len(resultArray) > 1 {
			errorMsg = cast.ToString(resultArray[1])
		}
		return &PvpScoreUpdateResult{
			Success:      false,
			ErrorMessage: errorMsg,
		}, nil
	}
	
	// 成功情况，需要5个元素
	if len(resultArray) < 5 {
		return nil, fmt.Errorf("success result array too short")
	}
	
	return &PvpScoreUpdateResult{
		Success:       true,
		NewMyScore:    cast.ToFloat64(resultArray[1]),
		NewRivalScore: cast.ToFloat64(resultArray[2]),
		OldMyScore:    cast.ToInt(resultArray[3]),
		OldRivalScore: cast.ToInt(resultArray[4]),
	}, nil
}
